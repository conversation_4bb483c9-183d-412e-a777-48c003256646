<script setup>
import { ref } from 'vue';
import { onLoad, onPullDownRefresh, onReachBottom } from '@dcloudio/uni-app';
import Tab from '@/components/Tab.vue';
import OrderItemCard from './components/OrderItemCard.vue';
import { getOrderList } from '@/api/order';

const tabs = ref([
  { id: 1, text: '全部', status: null, active: true },
  { id: 2, text: '待付款', status: 'UNPAID', active: false },
  { id: 3, text: '待发货', status: 'PENDING_SHIPMENT', active: false },
  { id: 4, text: '待收货', status: 'SHIPPED', active: false },
]);

const activeTabId = ref(1);
const orders = ref([]);
const pageNum = ref(1);
const pageSize = ref(10);
const hasMore = ref(true);
const loadMoreStatus = ref('more');

async function fetchOrders(isRefresh = false) {
  if (isRefresh) {
    pageNum.value = 1;
    orders.value = [];
    hasMore.value = true;
  }

  if (!hasMore.value) return;

  loadMoreStatus.value = 'loading';
  const currentTab = tabs.value.find((t) => t.id === activeTabId.value);

  try {
    const params = {
      pageNum: pageNum.value,
      pageSize: pageSize.value,
      status: currentTab.status,
    };
    const res = await getOrderList(params);
    const { rows, total } = res;
    orders.value = isRefresh ? rows : [...orders.value, ...rows];
    hasMore.value = orders.value.length < total;
    loadMoreStatus.value = hasMore.value ? 'more' : 'noMore';
    pageNum.value++;
  } catch (error) {
    console.error('Failed to fetch orders:', error);
    loadMoreStatus.value = 'more';
  }
}

function handleTabClick(tab) {
  activeTabId.value = tab.id;
  tabs.value.forEach((t) => {
    t.active = t.id === tab.id;
  });
  fetchOrders(true);
}

onLoad((options) => {
  // 根据URL参数设置对应的tab状态
  if (options.status) {
    const statusIndex = parseInt(options.status);
    if (statusIndex >= 1 && statusIndex <= 3) {
      // status=1对应待付款(id=2), status=2对应待发货(id=3), status=3对应待收货(id=4)
      const targetTabId = statusIndex + 1;
      activeTabId.value = targetTabId;

      // 更新tabs的active状态
      tabs.value.forEach((tab) => {
        tab.active = tab.id === targetTabId;
      });
    }
  }

  fetchOrders(true);
});

onPullDownRefresh(async () => {
  await fetchOrders(true);
  uni.stopPullDownRefresh();
});

onReachBottom(() => {
  if (hasMore.value) {
    fetchOrders();
  }
});
</script>

<template>
  <view class="order-page">
    <!-- Top Tab Bar -->
    <view class="tab-bar">
      <Tab
        v-for="tab in tabs"
        :key="tab.id"
        :text="tab.text"
        :active="tab.active"
        @click="handleTabClick(tab)"
      />
    </view>

    <!-- Order List -->
    <!-- Order List -->
    <view v-if="orders.length > 0" class="order-list">
      <OrderItemCard v-for="order in orders" :key="order.id" :order="order" />
      <uni-load-more :status="loadMoreStatus" />
    </view>

    <!-- Empty State -->
    <view v-else class="empty">
      <image class="empty-image" src="/static/no-order.png" mode="aspectFit" />
      <text class="empty-title">没有相关的订单</text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.order-page {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.tab-bar {
  width: 100%;
  height: 96rpx;
  background-color: #ffffff;
  border-bottom: 1.5rpx solid #eeeeee;
  display: flex;
  flex-direction: row;
  justify-content: space-around; /* Evenly distribute tabs */
  align-items: center;
}

.order-list {
  width: 100%;
  padding-top: 24rpx;
  display: flex;
  flex-direction: column;
  gap: 24rpx; /* Space between order cards */
}

.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
  flex-grow: 1;

  .empty-image {
    width: 400rpx;
    height: 400rpx;
  }

  .empty-title {
    font-size: 32rpx;
    line-height: 42rpx;
    color: #767676;
    font-weight: 500;
    margin-bottom: 16rpx;
  }
}
</style>