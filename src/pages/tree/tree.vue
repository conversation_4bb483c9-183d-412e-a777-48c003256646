<template>
  <view class="container">
    <view class="tree-list">
      <TreeCard
        v-for="tree in treeList"
        :key="tree.id"
        :tree="tree"
        @card-click="handleTreeClick"
        @adopt-click="handleAdoptClick"
        class="tree-item"
      />

      <!-- 加载更多组件 -->
      <uni-load-more
        :status="loadMoreStatus"
        @clickLoadMore="onLoadMore"
        :content-text="loadMoreText"
      />
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onPullDownRefresh, onReachBottom, onShow } from '@dcloudio/uni-app'
import TreeCard from '@/components/TreeCard.vue'
import { getFruitTreeList } from '@/api/tree'
import { checkPhoneBindingAndRedirect } from '@/utils/phoneBinding'

// 响应式数据
const treeList = ref([])
const loadMoreStatus = ref('more') // more, loading, noMore
const pageNum = ref(1)
const pageSize = ref(10)
const hasMore = ref(true)

// 加载更多文本配置
const loadMoreText = ref({
  contentdown: '上拉显示更多',
  contentrefresh: '正在加载...',
  contentnomore: '没有更多数据了'
})

// 加载数据
const loadData = async (isRefresh = false) => {
  try {
    if (isRefresh) {
      pageNum.value = 1
      hasMore.value = true
    }

    loadMoreStatus.value = 'loading'

    const res = await getFruitTreeList({
      pageNum: pageNum.value,
      pageSize: pageSize.value
    })

    const { rows, total } = res

    if (isRefresh) {
      treeList.value = rows
    } else {
      treeList.value.push(...rows)
    }

    hasMore.value = treeList.value.length < total
    loadMoreStatus.value = hasMore.value ? 'more' : 'noMore'
    pageNum.value++
  } catch (error) {
    console.error('加载数据失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
    loadMoreStatus.value = 'more'
  }
}

// 下拉刷新处理函数
const handlePullDownRefresh = async () => {
  await loadData(true)
  uni.stopPullDownRefresh()
}

// 上拉加载更多处理函数
const handleReachBottom = () => {
  if (hasMore.value && loadMoreStatus.value !== 'loading') {
    loadData(false)
  }
}

// uni-load-more组件点击事件
const onLoadMore = () => {
  if (hasMore.value && loadMoreStatus.value !== 'loading') {
    loadData(false)
  }
}

// 处理果树卡片点击
const handleTreeClick = (tree) => {
  console.log('点击了果树:', tree)
  // 跳转到果树详情页
  uni.navigateTo({
    url: `/pages/tree/detail/detail?id=${tree.id}`
  })
}

// 处理认养按钮点击
const handleAdoptClick = (tree) => {
  console.log('点击认养:', tree)
  // 认养逻辑已在TreeCard组件中处理
}

// 页面显示时检查手机号绑定状态
onShow(async () => {
  // 检查手机号绑定状态，如果未绑定则跳转到绑定页面
  if (!(await checkPhoneBindingAndRedirect('/pages/tree/tree'))) {
    return // 未绑定手机号，已跳转到绑定页面
  }

  loadData(true)
})

// 注册页面生命周期
onPullDownRefresh(handlePullDownRefresh)
onReachBottom(handleReachBottom)
</script>



<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding: 40rpx;
}

.tree-list {
  display: flex;
  flex-direction: column;
  gap: 40rpx;

  .tree-item {
    width: 100%;
  }
}
</style>
