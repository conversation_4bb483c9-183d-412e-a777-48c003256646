<template>
  <view class="benefits-container">
    <view 
      v-for="(benefit, index) in benefits" 
      :key="index"
      class="benefit-item"
      :class="{ 'no-border': index === benefits.length - 1 }"
    >
      <image 
        :src="`/static/${benefit.icon}.svg`" 
        mode="aspectFit"
        class="benefit-icon"
      />
      <view class="benefit-content">
        <text class="benefit-title">{{ benefit.title }}</text>
        <text class="benefit-separator">|</text>
        <text class="benefit-description">{{ benefit.text }}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
// Props
const props = defineProps({
  benefits: {
    type: Array,
    default: () => []
  }
})
</script>

<style lang="scss" scoped>
// 设计变量
$white-color: #ffffff;
$text-dark: #1a1a1a;
$text-gray: #666666;
$text-light-gray: #767676;
$border-gray: #eeeeee;

.benefits-container {
  background-color: $white-color;
  margin-bottom: 20rpx;
}

.benefit-item {
  display: flex;
  align-items: center;
  padding: 28rpx 40rpx;
  border-bottom: 2rpx solid $border-gray;
  
  &.no-border {
    border-bottom: none;
  }
}

.benefit-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 19rpx;
  flex-shrink: 0;
}

.benefit-content {
  display: flex;
  align-items: center;
  gap: 8rpx;
  flex: 1;
  
  .benefit-title {
    font-size: 28rpx;
    line-height: 33rpx;
    color: $text-dark;
  }
  
  .benefit-separator {
    font-size: 28rpx;
    line-height: 33rpx;
    color: $text-gray;
  }
  
  .benefit-description {
    font-size: 28rpx;
    line-height: 33rpx;
    color: $text-gray;
    flex: 1;
  }
}
</style>
