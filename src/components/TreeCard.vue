<template>
  <view class="tree-card" @click="handleCardClick">
    <!-- 图片区域 -->
    <view class="image-container">
      <image class="tree-image" :src="tree.imageUrl" mode="aspectFill" />
    </view>

    <!-- 内容区域 -->
    <view class="content-container">
      <!-- 产品名称 -->
      <text class="tree-name">{{ tree.name }}</text>

      <!-- 描述 -->
      <text class="tree-description">{{ tree.description }}</text>

      <!-- 价格和按钮区域 -->
      <view class="action-row">
        <view class="price-info">
          <text class="price">¥ {{ tree.price.toFixed(2) }}</text>
          <text class="remaining">剩余 {{ tree.stock }}</text>
        </view>
        <view class="adopt-button" @click.stop="handleAdoptClick">
          <text class="button-text">选择认养</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'

// 定义props
const props = defineProps({
  tree: {
    type: Object,
    required: true,
    default: () => ({
      id: '',
      name: '产品名称01',
      description: '此处为产品的简单介绍，给用户提供简单认知',
      price: 198.00,
      stock: 1669,
      imageUrl: 'https://images.unsplash.com/photo-1560806887-1e4cd0b6cbd6?w=750&h=378&fit=crop'
    })
  }
})

// 定义事件
const emit = defineEmits(['card-click', 'adopt-click'])

// 卡片点击 - 查看详情
const handleCardClick = () => {
  emit('card-click', props.tree)
  // 可以在这里添加页面跳转逻辑
  // uni.navigateTo({
  //   url: `/pages/tree-detail/tree-detail?id=${props.tree.id}`
  // })
}

// 认养按钮点击 - 直接认养
const handleAdoptClick = () => {
  emit('adopt-click', props.tree)
  uni.showModal({
    title: '确认认养',
    content: `确定要认养"${props.tree.name}"吗？`,
    success: (res) => {
      if (res.confirm) {
        // 处理认养逻辑
        uni.showToast({
          title: '认养成功',
          icon: 'success'
        })
      }
    }
  })
}
</script>

<style lang="scss" scoped>
// 颜色变量
$primary-color: #dd3c29;    // 主色调（认养按钮）
$white-color: #ffffff;      // 白色背景
$text-primary: #1a1a1a;     // 主要文字（产品名称）
$text-secondary: #999999;   // 次要文字（描述、剩余）
$text-price: #ea0000;       // 价格文字

// 尺寸变量
$card-radius: 16rpx;        // 卡片圆角
$image-height: 378rpx;      // 图片高度
$content-padding: 40rpx;    // 内容区内边距
$button-radius: 32rpx;      // 按钮圆角

// 混入
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.tree-card {
  width: 100%;
  background-color: $white-color;
  border-radius: $card-radius;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  
  // 添加点击效果
  &:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }
}

.image-container {
  width: 100%;
  height: $image-height;

  .tree-image {
    width: 100%;
    height: 100%;
  }
}

.content-container {
  padding: $content-padding;

  .tree-name {
    display: block;
    font-size: 32rpx;
    font-weight: 500;
    line-height: 38rpx;
    color: $text-primary;
    margin-bottom: 20rpx;
  }

  .tree-description {
    display: block;
    font-size: 30rpx;
    line-height: 35rpx;
    color: $text-secondary;
    margin-bottom: 28rpx;
  }
  
  .action-row {
    @include flex-between;
    
    .price-info {
      @include flex-between;
      width: 310rpx;
      
      .price {
        font-size: 36rpx;
        font-weight: 700;
        line-height: 42rpx;
        color: $text-price;
      }
      
      .remaining {
        font-size: 28rpx;
        line-height: 33rpx;
        color: $text-secondary;
      }
    }
    
    .adopt-button {
      @include flex-center;
      width: 166rpx;
      height: 64rpx;
      background-color: $primary-color;
      border-radius: $button-radius;
      
      // 添加点击效果
      &:active {
        opacity: 0.8;
      }
      
      .button-text {
        font-size: 30rpx;
        line-height: 35rpx;
        color: $white-color;
      }
    }
  }
}
</style>
